const express = require("express");
const router = express.Router();
const { protect } = require("../middleware/auth");
const {
  validateObjectId,
  validatePagination,
  validateConversationUpdate,
} = require("../middleware/validation");
const conversationService = require("../services/conversationService");
const logger = require("../utils/logger");

// @desc    Get user's conversations
// @route   GET /api/conversations
// @access  Private
router.get("/", protect, validatePagination, async (req, res, next) => {
  try {
    const userId = req.user._id;
    const options = {
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 20,
      status: req.query.status || "active",
      sortBy: req.query.sortBy || "lastActivity",
      sortOrder: req.query.sortOrder || "desc",
    };

    const result = await conversationService.getUserConversations(
      userId,
      options
    );

    res.status(200).json({
      success: true,
      data: result.conversations,
      pagination: result.pagination,
    });
  } catch (error) {
    logger.error("Get conversations error:", error);
    next(error);
  }
});

// @desc    Get specific conversation
// @route   GET /api/conversations/:id
// @access  Private
router.get("/:id", protect, validateObjectId, async (req, res, next) => {
  try {
    const conversationId = req.params.id;
    const userId = req.user._id;

    const conversation = await conversationService.getConversation(
      conversationId,
      userId
    );

    res.status(200).json({
      success: true,
      data: conversation,
    });
  } catch (error) {
    logger.error("Get conversation error:", error);
    next(error);
  }
});

// @desc    Update conversation
// @route   PUT /api/conversations/:id
// @access  Private
router.put(
  "/:id",
  protect,
  validateObjectId,
  validateConversationUpdate,
  async (req, res, next) => {
    try {
      const conversationId = req.params.id;
      const userId = req.user._id;
      const updates = req.body;

      const conversation = await conversationService.updateConversation(
        conversationId,
        userId,
        updates
      );

      res.status(200).json({
        success: true,
        data: conversation,
      });
    } catch (error) {
      logger.error("Update conversation error:", error);
      next(error);
    }
  }
);

// @desc    Delete conversation
// @route   DELETE /api/conversations/:id
// @access  Private
router.delete("/:id", protect, validateObjectId, async (req, res, next) => {
  try {
    const conversationId = req.params.id;
    const userId = req.user._id;

    await conversationService.deleteConversation(conversationId, userId);

    res.status(200).json({
      success: true,
      message: "Conversation deleted successfully",
    });
  } catch (error) {
    logger.error("Delete conversation error:", error);
    next(error);
  }
});

// @desc    Archive conversation
// @route   PUT /api/conversations/:id/archive
// @access  Private
router.put(
  "/:id/archive",
  protect,
  validateObjectId,
  async (req, res, next) => {
    try {
      const conversationId = req.params.id;
      const userId = req.user._id;

      const conversation = await conversationService.updateConversation(
        conversationId,
        userId,
        { status: "archived" }
      );

      res.status(200).json({
        success: true,
        data: conversation,
        message: "Conversation archived successfully",
      });
    } catch (error) {
      logger.error("Archive conversation error:", error);
      next(error);
    }
  }
);

// @desc    Restore conversation
// @route   PUT /api/conversations/:id/restore
// @access  Private
router.put(
  "/:id/restore",
  protect,
  validateObjectId,
  async (req, res, next) => {
    try {
      const conversationId = req.params.id;
      const userId = req.user._id;

      const conversation = await conversationService.updateConversation(
        conversationId,
        userId,
        { status: "active" }
      );

      res.status(200).json({
        success: true,
        data: conversation,
        message: "Conversation restored successfully",
      });
    } catch (error) {
      logger.error("Restore conversation error:", error);
      next(error);
    }
  }
);

module.exports = router;
