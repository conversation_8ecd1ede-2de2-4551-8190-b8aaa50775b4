const { OpenAI } = require("openai");
const logger = require("../utils/logger");

class OpenAIService {
  constructor() {
    this.client = null;
    this.initializeClient();
  }

  initializeClient() {
    if (process.env.OPENAI_API_KEY) {
      this.client = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });
    } else {
      logger.warn("OpenAI API key not provided");
    }
  }

  /**
   * Generate response using OpenAI
   * @param {Object} config - AI configuration
   * @param {Array} messages - Conversation messages
   * @param {Object} options - Additional options
   * @returns {Object} AI response
   */
  async generateResponse(config, messages, options = {}) {
    if (!this.client) {
      throw new Error(
        "OpenAI client not initialized. Please provide OPENAI_API_KEY."
      );
    }

    try {
      const requestMessages = this.formatMessages(
        messages,
        config.personality?.systemPrompt
      );

      const response = await this.client.chat.completions.create({
        model: config.model || "gpt-3.5-turbo",
        messages: requestMessages,
        temperature: config.temperature || 0.7,
        max_tokens: config.maxTokens || 2000,
        top_p: config.topP || 1,
        frequency_penalty: config.frequencyPenalty || 0,
        presence_penalty: config.presencePenalty || 0,
        ...options,
      });

      const choice = response.choices[0];

      return {
        content: choice.message.content,
        role: "assistant",
        metadata: {
          model: response.model,
          tokens: response.usage.total_tokens,
          promptTokens: response.usage.prompt_tokens,
          completionTokens: response.usage.completion_tokens,
          cost: this.calculateCost(response.model, response.usage),
          finishReason: choice.finish_reason,
        },
      };
    } catch (error) {
      logger.error("OpenAI API error:", error);
      throw this.handleOpenAIError(error);
    }
  }

  /**
   * Generate streaming response using OpenAI
   * @param {Object} config - AI configuration
   * @param {Array} messages - Conversation messages
   * @param {Object} options - Additional options
   * @returns {AsyncGenerator} Streaming response
   */
  async *generateStreamingResponse(config, messages, options = {}) {
    if (!this.client) {
      throw new Error(
        "OpenAI client not initialized. Please provide OPENAI_API_KEY."
      );
    }

    try {
      const requestMessages = this.formatMessages(
        messages,
        config.personality?.systemPrompt
      );

      const stream = await this.client.chat.completions.create({
        model: config.model || "gpt-3.5-turbo",
        messages: requestMessages,
        temperature: config.temperature || 0.7,
        max_tokens: config.maxTokens || 2000,
        top_p: config.topP || 1,
        frequency_penalty: config.frequencyPenalty || 0,
        presence_penalty: config.presencePenalty || 0,
        stream: true,
        ...options,
      });

      let fullContent = "";

      for await (const chunk of stream) {
        const delta = chunk.choices[0]?.delta;

        if (delta?.content) {
          fullContent += delta.content;

          yield {
            content: delta.content,
            role: "assistant",
            type: "chunk",
            metadata: {
              model: chunk.model,
              tokens: 1, // Approximate for streaming
            },
          };
        }

        if (chunk.choices[0]?.finish_reason) {
          yield {
            content: "",
            role: "assistant",
            type: "done",
            metadata: {
              model: chunk.model,
              finishReason: chunk.choices[0].finish_reason,
              fullContent,
            },
          };
        }
      }
    } catch (error) {
      logger.error("OpenAI streaming error:", error);
      throw this.handleOpenAIError(error);
    }
  }

  /**
   * Format messages for OpenAI API
   * @param {Array} messages - Raw messages
   * @param {string} systemPrompt - System prompt
   * @returns {Array} Formatted messages
   */
  formatMessages(messages, systemPrompt) {
    const formattedMessages = [];

    // Add system prompt if provided
    if (systemPrompt) {
      formattedMessages.push({
        role: "system",
        content: systemPrompt,
      });
    }

    // Add conversation messages
    messages.forEach((msg) => {
      formattedMessages.push({
        role: msg.role,
        content: msg.content,
      });
    });

    return formattedMessages;
  }

  /**
   * Calculate cost based on model and usage
   * @param {string} model - Model name
   * @param {Object} usage - Token usage
   * @returns {number} Cost in USD
   */
  calculateCost(model, usage) {
    // Pricing as of 2024 (per 1K tokens)
    const pricing = {
      "gpt-4": { prompt: 0.03, completion: 0.06 },
      "gpt-4-turbo": { prompt: 0.01, completion: 0.03 },
      "gpt-3.5-turbo": { prompt: 0.0015, completion: 0.002 },
      "gpt-3.5-turbo-16k": { prompt: 0.003, completion: 0.004 },
    };

    const modelPricing = pricing[model] || pricing["gpt-3.5-turbo"];

    const promptCost = (usage.prompt_tokens / 1000) * modelPricing.prompt;
    const completionCost =
      (usage.completion_tokens / 1000) * modelPricing.completion;

    return promptCost + completionCost;
  }

  /**
   * Handle OpenAI specific errors
   * @param {Error} error - Original error
   * @returns {Error} Formatted error
   */
  handleOpenAIError(error) {
    if (error.status === 401) {
      return new Error("Invalid OpenAI API key");
    } else if (error.status === 429) {
      return new Error("OpenAI rate limit exceeded");
    } else if (error.status === 500) {
      return new Error("OpenAI server error");
    } else {
      return new Error(`OpenAI API error: ${error.message}`);
    }
  }

  /**
   * Validate OpenAI configuration
   * @param {Object} config - Configuration to validate
   * @returns {boolean} Whether configuration is valid
   */
  validateConfig(config) {
    const validModels = [
      "gpt-4",
      "gpt-4-turbo",
      "gpt-3.5-turbo",
      "gpt-3.5-turbo-16k",
    ];

    return validModels.includes(config.model);
  }

  /**
   * Get available OpenAI models
   * @returns {Array} Available models
   */
  async getAvailableModels() {
    if (!this.client) {
      return [];
    }

    try {
      const models = await this.client.models.list();
      return models.data
        .filter((model) => model.id.includes("gpt"))
        .map((model) => ({
          id: model.id,
          name: model.id,
          provider: "openai",
        }));
    } catch (error) {
      logger.error("Error fetching OpenAI models:", error);
      return [];
    }
  }
}

module.exports = OpenAIService;
