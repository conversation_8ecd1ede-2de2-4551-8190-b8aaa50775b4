const OpenAIService = require('./openaiService');
const AnthropicService = require('./anthropicService');
const logger = require('../utils/logger');

class AIService {
  constructor() {
    this.services = {
      openai: new OpenAIService(),
      anthropic: new AnthropicService()
    };
  }

  /**
   * Get AI service instance based on provider
   * @param {string} provider - AI service provider (openai, anthropic, etc.)
   * @returns {Object} AI service instance
   */
  getService(provider) {
    const service = this.services[provider];
    if (!service) {
      throw new Error(`Unsupported AI provider: ${provider}`);
    }
    return service;
  }

  /**
   * Generate AI response
   * @param {Object} config - AI configuration
   * @param {Array} messages - Conversation messages
   * @param {Object} options - Additional options
   * @returns {Object} AI response
   */
  async generateResponse(config, messages, options = {}) {
    try {
      const service = this.getService(config.provider);
      
      const startTime = Date.now();
      const response = await service.generateResponse(config, messages, options);
      const processingTime = Date.now() - startTime;

      // Add metadata to response
      response.metadata = {
        ...response.metadata,
        processingTime,
        provider: config.provider,
        model: config.model,
        timestamp: new Date().toISOString()
      };

      logger.info('AI response generated', {
        provider: config.provider,
        model: config.model,
        processingTime,
        tokens: response.metadata.tokens
      });

      return response;
    } catch (error) {
      logger.error('AI service error:', {
        provider: config.provider,
        error: error.message,
        stack: error.stack
      });

      // Create standardized error
      const aiError = new Error('AI service temporarily unavailable');
      aiError.type = 'AI_SERVICE_ERROR';
      aiError.originalError = error;
      throw aiError;
    }
  }

  /**
   * Generate streaming AI response
   * @param {Object} config - AI configuration
   * @param {Array} messages - Conversation messages
   * @param {Object} options - Additional options
   * @returns {AsyncGenerator} Streaming response
   */
  async* generateStreamingResponse(config, messages, options = {}) {
    try {
      const service = this.getService(config.provider);
      
      if (!service.generateStreamingResponse) {
        throw new Error(`Streaming not supported for provider: ${config.provider}`);
      }

      const startTime = Date.now();
      let totalTokens = 0;

      for await (const chunk of service.generateStreamingResponse(config, messages, options)) {
        if (chunk.metadata?.tokens) {
          totalTokens += chunk.metadata.tokens;
        }
        yield chunk;
      }

      const processingTime = Date.now() - startTime;
      
      logger.info('AI streaming response completed', {
        provider: config.provider,
        model: config.model,
        processingTime,
        totalTokens
      });

    } catch (error) {
      logger.error('AI streaming service error:', {
        provider: config.provider,
        error: error.message,
        stack: error.stack
      });

      const aiError = new Error('AI streaming service temporarily unavailable');
      aiError.type = 'AI_SERVICE_ERROR';
      aiError.originalError = error;
      throw aiError;
    }
  }

  /**
   * Validate AI configuration
   * @param {Object} config - AI configuration to validate
   * @returns {boolean} Whether configuration is valid
   */
  validateConfig(config) {
    try {
      const service = this.getService(config.provider);
      return service.validateConfig ? service.validateConfig(config) : true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get available models for a provider
   * @param {string} provider - AI service provider
   * @returns {Array} Available models
   */
  async getAvailableModels(provider) {
    try {
      const service = this.getService(provider);
      return service.getAvailableModels ? await service.getAvailableModels() : [];
    } catch (error) {
      logger.error('Error getting available models:', error);
      return [];
    }
  }

  /**
   * Estimate cost for a request
   * @param {Object} config - AI configuration
   * @param {Array} messages - Conversation messages
   * @returns {number} Estimated cost in USD
   */
  estimateCost(config, messages) {
    try {
      const service = this.getService(config.provider);
      return service.estimateCost ? service.estimateCost(config, messages) : 0;
    } catch (error) {
      logger.error('Error estimating cost:', error);
      return 0;
    }
  }
}

module.exports = new AIService();
