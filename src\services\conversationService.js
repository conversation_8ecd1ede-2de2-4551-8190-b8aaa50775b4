const { Conversation, AgentConfig } = require('../models');
const aiService = require('./aiService');
const logger = require('../utils/logger');

class ConversationService {
  /**
   * Create a new conversation
   * @param {string} userId - User ID
   * @param {string} agentConfigId - Agent configuration ID
   * @param {string} title - Conversation title
   * @returns {Object} Created conversation
   */
  async createConversation(userId, agentConfigId, title = 'New Conversation') {
    try {
      // Verify agent config exists and user has access
      const agentConfig = await AgentConfig.findOne({
        _id: agentConfigId,
        $or: [
          { userId },
          { isPublic: true }
        ],
        status: 'active'
      });

      if (!agentConfig) {
        throw new Error('Agent configuration not found or access denied');
      }

      const conversation = new Conversation({
        userId,
        title,
        agentConfig: agentConfigId,
        messages: [],
        status: 'active'
      });

      await conversation.save();
      await conversation.populate('agentConfig');

      logger.info('Conversation created', {
        conversationId: conversation._id,
        userId,
        agentConfigId
      });

      return conversation;
    } catch (error) {
      logger.error('Error creating conversation:', error);
      throw error;
    }
  }

  /**
   * Send message and get AI response
   * @param {string} conversationId - Conversation ID
   * @param {string} userId - User ID
   * @param {string} message - User message
   * @param {Object} options - Additional options
   * @returns {Object} AI response
   */
  async sendMessage(conversationId, userId, message, options = {}) {
    try {
      // Get conversation with agent config
      const conversation = await Conversation.findOne({
        _id: conversationId,
        userId,
        status: 'active'
      }).populate('agentConfig');

      if (!conversation) {
        throw new Error('Conversation not found or access denied');
      }

      // Add user message to conversation
      await conversation.addMessage('user', message);

      // Get conversation context
      const context = conversation.getContext(conversation.agentConfig.constraints.maxConversationLength);

      // Generate AI response
      const aiResponse = await aiService.generateResponse(
        conversation.agentConfig.modelConfig,
        context,
        options
      );

      // Add AI response to conversation
      await conversation.addMessage('assistant', aiResponse.content, aiResponse.metadata);

      // Increment agent usage
      await conversation.agentConfig.incrementUsage();

      logger.info('Message processed', {
        conversationId,
        userId,
        messageLength: message.length,
        responseLength: aiResponse.content.length,
        tokens: aiResponse.metadata.tokens
      });

      return {
        message: aiResponse.content,
        metadata: aiResponse.metadata,
        conversation: {
          id: conversation._id,
          title: conversation.title,
          messageCount: conversation.messageCount
        }
      };
    } catch (error) {
      logger.error('Error sending message:', error);
      throw error;
    }
  }

  /**
   * Send message and get streaming AI response
   * @param {string} conversationId - Conversation ID
   * @param {string} userId - User ID
   * @param {string} message - User message
   * @param {Object} options - Additional options
   * @returns {AsyncGenerator} Streaming AI response
   */
  async* sendMessageStreaming(conversationId, userId, message, options = {}) {
    try {
      // Get conversation with agent config
      const conversation = await Conversation.findOne({
        _id: conversationId,
        userId,
        status: 'active'
      }).populate('agentConfig');

      if (!conversation) {
        throw new Error('Conversation not found or access denied');
      }

      // Add user message to conversation
      await conversation.addMessage('user', message);

      // Get conversation context
      const context = conversation.getContext(conversation.agentConfig.constraints.maxConversationLength);

      let fullResponse = '';
      let responseMetadata = {};

      // Generate streaming AI response
      for await (const chunk of aiService.generateStreamingResponse(
        conversation.agentConfig.modelConfig,
        context,
        options
      )) {
        if (chunk.type === 'chunk') {
          fullResponse += chunk.content;
        } else if (chunk.type === 'done') {
          responseMetadata = chunk.metadata;
        }

        yield {
          ...chunk,
          conversation: {
            id: conversation._id,
            title: conversation.title,
            messageCount: conversation.messageCount + 1
          }
        };
      }

      // Add complete AI response to conversation
      if (fullResponse) {
        await conversation.addMessage('assistant', fullResponse, responseMetadata);
        await conversation.agentConfig.incrementUsage();

        logger.info('Streaming message processed', {
          conversationId,
          userId,
          messageLength: message.length,
          responseLength: fullResponse.length
        });
      }
    } catch (error) {
      logger.error('Error in streaming message:', error);
      throw error;
    }
  }

  /**
   * Get conversation by ID
   * @param {string} conversationId - Conversation ID
   * @param {string} userId - User ID
   * @returns {Object} Conversation
   */
  async getConversation(conversationId, userId) {
    try {
      const conversation = await Conversation.findOne({
        _id: conversationId,
        userId,
        status: { $ne: 'deleted' }
      }).populate('agentConfig');

      if (!conversation) {
        throw new Error('Conversation not found or access denied');
      }

      return conversation;
    } catch (error) {
      logger.error('Error getting conversation:', error);
      throw error;
    }
  }

  /**
   * Get user's conversations
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @returns {Array} Conversations
   */
  async getUserConversations(userId, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        status = 'active',
        sortBy = 'lastActivity',
        sortOrder = 'desc'
      } = options;

      const skip = (page - 1) * limit;
      const sort = { [`metadata.${sortBy}`]: sortOrder === 'desc' ? -1 : 1 };

      const conversations = await Conversation.find({
        userId,
        status
      })
        .populate('agentConfig', 'name description')
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .select('-messages'); // Exclude messages for list view

      const total = await Conversation.countDocuments({ userId, status });

      return {
        conversations,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Error getting user conversations:', error);
      throw error;
    }
  }

  /**
   * Update conversation
   * @param {string} conversationId - Conversation ID
   * @param {string} userId - User ID
   * @param {Object} updates - Updates to apply
   * @returns {Object} Updated conversation
   */
  async updateConversation(conversationId, userId, updates) {
    try {
      const conversation = await Conversation.findOneAndUpdate(
        {
          _id: conversationId,
          userId,
          status: { $ne: 'deleted' }
        },
        updates,
        { new: true, runValidators: true }
      ).populate('agentConfig');

      if (!conversation) {
        throw new Error('Conversation not found or access denied');
      }

      logger.info('Conversation updated', {
        conversationId,
        userId,
        updates: Object.keys(updates)
      });

      return conversation;
    } catch (error) {
      logger.error('Error updating conversation:', error);
      throw error;
    }
  }

  /**
   * Delete conversation
   * @param {string} conversationId - Conversation ID
   * @param {string} userId - User ID
   * @returns {boolean} Success status
   */
  async deleteConversation(conversationId, userId) {
    try {
      const conversation = await Conversation.findOneAndUpdate(
        {
          _id: conversationId,
          userId,
          status: { $ne: 'deleted' }
        },
        { status: 'deleted' },
        { new: true }
      );

      if (!conversation) {
        throw new Error('Conversation not found or access denied');
      }

      logger.info('Conversation deleted', {
        conversationId,
        userId
      });

      return true;
    } catch (error) {
      logger.error('Error deleting conversation:', error);
      throw error;
    }
  }
}

module.exports = new ConversationService();
