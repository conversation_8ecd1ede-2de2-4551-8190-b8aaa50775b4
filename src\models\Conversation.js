const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  role: {
    type: String,
    enum: ['user', 'assistant', 'system'],
    required: true
  },
  content: {
    type: String,
    required: true,
    maxlength: [10000, 'Message content cannot exceed 10000 characters']
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  metadata: {
    model: String,
    tokens: Number,
    cost: Number,
    processingTime: Number,
    confidence: Number
  }
});

const conversationSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  title: {
    type: String,
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters'],
    default: 'New Conversation'
  },
  messages: [messageSchema],
  agentConfig: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'AgentConfig',
    required: true
  },
  status: {
    type: String,
    enum: ['active', 'archived', 'deleted'],
    default: 'active'
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: [50, 'Tag cannot exceed 50 characters']
  }],
  summary: {
    type: String,
    maxlength: [1000, 'Summary cannot exceed 1000 characters']
  },
  metadata: {
    totalMessages: {
      type: Number,
      default: 0
    },
    totalTokens: {
      type: Number,
      default: 0
    },
    totalCost: {
      type: Number,
      default: 0
    },
    averageResponseTime: {
      type: Number,
      default: 0
    },
    lastActivity: {
      type: Date,
      default: Date.now
    }
  },
  settings: {
    autoSave: {
      type: Boolean,
      default: true
    },
    maxMessages: {
      type: Number,
      default: 100,
      min: [1, 'Max messages must be at least 1'],
      max: [1000, 'Max messages cannot exceed 1000']
    },
    retentionDays: {
      type: Number,
      default: 30,
      min: [1, 'Retention days must be at least 1']
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for message count
conversationSchema.virtual('messageCount').get(function() {
  return this.messages.length;
});

// Virtual for last message
conversationSchema.virtual('lastMessage').get(function() {
  return this.messages.length > 0 ? this.messages[this.messages.length - 1] : null;
});

// Indexes for better query performance
conversationSchema.index({ userId: 1, createdAt: -1 });
conversationSchema.index({ userId: 1, status: 1 });
conversationSchema.index({ 'metadata.lastActivity': -1 });
conversationSchema.index({ tags: 1 });

// Pre-save middleware to update metadata
conversationSchema.pre('save', function(next) {
  if (this.isModified('messages')) {
    this.metadata.totalMessages = this.messages.length;
    this.metadata.totalTokens = this.messages.reduce((total, msg) => {
      return total + (msg.metadata?.tokens || 0);
    }, 0);
    this.metadata.totalCost = this.messages.reduce((total, msg) => {
      return total + (msg.metadata?.cost || 0);
    }, 0);
    
    const responseTimes = this.messages
      .filter(msg => msg.metadata?.processingTime)
      .map(msg => msg.metadata.processingTime);
    
    if (responseTimes.length > 0) {
      this.metadata.averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    }
    
    this.metadata.lastActivity = new Date();
  }
  next();
});

// Instance method to add message
conversationSchema.methods.addMessage = function(role, content, metadata = {}) {
  const message = {
    role,
    content,
    metadata,
    timestamp: new Date()
  };
  
  this.messages.push(message);
  
  // Enforce max messages limit
  if (this.messages.length > this.settings.maxMessages) {
    this.messages = this.messages.slice(-this.settings.maxMessages);
  }
  
  return this.save();
};

// Instance method to get conversation context
conversationSchema.methods.getContext = function(maxMessages = 10) {
  return this.messages.slice(-maxMessages).map(msg => ({
    role: msg.role,
    content: msg.content
  }));
};

// Static method to find active conversations
conversationSchema.statics.findActive = function(userId) {
  return this.find({ userId, status: 'active' }).sort({ 'metadata.lastActivity': -1 });
};

// Static method to cleanup old conversations
conversationSchema.statics.cleanupOld = function() {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - 90); // 90 days ago
  
  return this.updateMany(
    { 
      'metadata.lastActivity': { $lt: cutoffDate },
      status: 'active'
    },
    { status: 'archived' }
  );
};

module.exports = mongoose.model('Conversation', conversationSchema);
