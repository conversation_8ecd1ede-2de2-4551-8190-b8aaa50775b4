const Anthropic = require('@anthropic-ai/sdk');
const logger = require('../utils/logger');

class AnthropicService {
  constructor() {
    this.client = null;
    this.initializeClient();
  }

  initializeClient() {
    if (process.env.ANTHROPIC_API_KEY) {
      this.client = new Anthropic({
        apiKey: process.env.ANTHROPIC_API_KEY
      });
    } else {
      logger.warn('Anthropic API key not provided');
    }
  }

  /**
   * Generate response using Anthropic Claude
   * @param {Object} config - AI configuration
   * @param {Array} messages - Conversation messages
   * @param {Object} options - Additional options
   * @returns {Object} AI response
   */
  async generateResponse(config, messages, options = {}) {
    if (!this.client) {
      throw new Error('Anthropic client not initialized. Please provide ANTHROPIC_API_KEY.');
    }

    try {
      const { formattedMessages, systemPrompt } = this.formatMessages(messages, config.personality?.systemPrompt);
      
      const response = await this.client.messages.create({
        model: config.model || 'claude-3-sonnet-20240229',
        max_tokens: config.maxTokens || 2000,
        temperature: config.temperature || 0.7,
        system: systemPrompt,
        messages: formattedMessages,
        ...options
      });

      return {
        content: response.content[0].text,
        role: 'assistant',
        metadata: {
          model: response.model,
          tokens: response.usage.input_tokens + response.usage.output_tokens,
          promptTokens: response.usage.input_tokens,
          completionTokens: response.usage.output_tokens,
          cost: this.calculateCost(response.model, response.usage),
          stopReason: response.stop_reason
        }
      };
    } catch (error) {
      logger.error('Anthropic API error:', error);
      throw this.handleAnthropicError(error);
    }
  }

  /**
   * Generate streaming response using Anthropic Claude
   * @param {Object} config - AI configuration
   * @param {Array} messages - Conversation messages
   * @param {Object} options - Additional options
   * @returns {AsyncGenerator} Streaming response
   */
  async* generateStreamingResponse(config, messages, options = {}) {
    if (!this.client) {
      throw new Error('Anthropic client not initialized. Please provide ANTHROPIC_API_KEY.');
    }

    try {
      const { formattedMessages, systemPrompt } = this.formatMessages(messages, config.personality?.systemPrompt);
      
      const stream = await this.client.messages.create({
        model: config.model || 'claude-3-sonnet-20240229',
        max_tokens: config.maxTokens || 2000,
        temperature: config.temperature || 0.7,
        system: systemPrompt,
        messages: formattedMessages,
        stream: true,
        ...options
      });

      let fullContent = '';
      
      for await (const chunk of stream) {
        if (chunk.type === 'content_block_delta' && chunk.delta?.text) {
          fullContent += chunk.delta.text;
          
          yield {
            content: chunk.delta.text,
            role: 'assistant',
            type: 'chunk',
            metadata: {
              model: config.model,
              tokens: 1 // Approximate for streaming
            }
          };
        }

        if (chunk.type === 'message_stop') {
          yield {
            content: '',
            role: 'assistant',
            type: 'done',
            metadata: {
              model: config.model,
              stopReason: chunk.stop_reason,
              fullContent
            }
          };
        }
      }
    } catch (error) {
      logger.error('Anthropic streaming error:', error);
      throw this.handleAnthropicError(error);
    }
  }

  /**
   * Format messages for Anthropic API
   * @param {Array} messages - Raw messages
   * @param {string} systemPrompt - System prompt
   * @returns {Object} Formatted messages and system prompt
   */
  formatMessages(messages, systemPrompt) {
    const formattedMessages = [];

    // Anthropic doesn't include system messages in the messages array
    // Filter out system messages and use them as system prompt
    messages.forEach(msg => {
      if (msg.role !== 'system') {
        formattedMessages.push({
          role: msg.role === 'assistant' ? 'assistant' : 'user',
          content: msg.content
        });
      }
    });

    return {
      formattedMessages,
      systemPrompt: systemPrompt || 'You are a helpful AI assistant.'
    };
  }

  /**
   * Calculate cost based on model and usage
   * @param {string} model - Model name
   * @param {Object} usage - Token usage
   * @returns {number} Cost in USD
   */
  calculateCost(model, usage) {
    // Pricing as of 2024 (per 1M tokens)
    const pricing = {
      'claude-3-opus-20240229': { input: 15, output: 75 },
      'claude-3-sonnet-20240229': { input: 3, output: 15 },
      'claude-3-haiku-20240307': { input: 0.25, output: 1.25 }
    };

    const modelPricing = pricing[model] || pricing['claude-3-sonnet-20240229'];
    
    const inputCost = (usage.input_tokens / 1000000) * modelPricing.input;
    const outputCost = (usage.output_tokens / 1000000) * modelPricing.output;
    
    return inputCost + outputCost;
  }

  /**
   * Handle Anthropic specific errors
   * @param {Error} error - Original error
   * @returns {Error} Formatted error
   */
  handleAnthropicError(error) {
    if (error.status === 401) {
      return new Error('Invalid Anthropic API key');
    } else if (error.status === 429) {
      return new Error('Anthropic rate limit exceeded');
    } else if (error.status === 500) {
      return new Error('Anthropic server error');
    } else {
      return new Error(`Anthropic API error: ${error.message}`);
    }
  }

  /**
   * Validate Anthropic configuration
   * @param {Object} config - Configuration to validate
   * @returns {boolean} Whether configuration is valid
   */
  validateConfig(config) {
    const validModels = [
      'claude-3-opus-20240229',
      'claude-3-sonnet-20240229',
      'claude-3-haiku-20240307'
    ];
    
    return validModels.includes(config.model);
  }

  /**
   * Get available Anthropic models
   * @returns {Array} Available models
   */
  async getAvailableModels() {
    // Anthropic doesn't have a models endpoint, return known models
    return [
      { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus', provider: 'anthropic' },
      { id: 'claude-3-sonnet-20240229', name: 'Claude 3 Sonnet', provider: 'anthropic' },
      { id: 'claude-3-haiku-20240307', name: 'Claude 3 Haiku', provider: 'anthropic' }
    ];
  }
}

module.exports = AnthropicService;
