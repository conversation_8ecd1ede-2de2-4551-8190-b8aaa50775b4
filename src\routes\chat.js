const express = require("express");
const router = express.Router();
const { protect } = require("../middleware/auth");
const { validateChatMessage } = require("../middleware/validation");
const conversationService = require("../services/conversationService");
const { AgentConfig } = require("../models");
const logger = require("../utils/logger");

// @desc    Send a chat message
// @route   POST /api/chat
// @access  Private
router.post("/", protect, validateChatMessage, async (req, res, next) => {
  try {
    const { message, conversationId, agentConfigId } = req.body;
    const userId = req.user._id;

    let targetConversationId = conversationId;

    // If no conversation ID provided, create a new conversation
    if (!targetConversationId) {
      if (!agentConfigId) {
        // Use user's default agent config or create one
        let defaultAgentConfig = await AgentConfig.findOne({
          userId,
          isDefault: true,
          status: "active",
        });

        if (!defaultAgentConfig) {
          // Create a default agent config
          defaultAgentConfig = new AgentConfig({
            name: "Default Assistant",
            description: "Default AI assistant configuration",
            userId,
            isDefault: true,
            personality: {
              systemPrompt:
                "You are a helpful, harmless, and honest AI assistant.",
              tone: "friendly",
            },
            modelConfig: {
              provider: "openai",
              model: process.env.DEFAULT_AI_MODEL || "gpt-3.5-turbo",
              temperature: parseFloat(process.env.TEMPERATURE) || 0.7,
              maxTokens: parseInt(process.env.MAX_TOKENS) || 2000,
            },
          });
          await defaultAgentConfig.save();
        }

        const newConversation = await conversationService.createConversation(
          userId,
          defaultAgentConfig._id,
          "New Chat"
        );
        targetConversationId = newConversation._id;
      } else {
        const newConversation = await conversationService.createConversation(
          userId,
          agentConfigId,
          "New Chat"
        );
        targetConversationId = newConversation._id;
      }
    }

    // Send message and get response
    const response = await conversationService.sendMessage(
      targetConversationId,
      userId,
      message
    );

    res.status(200).json({
      success: true,
      data: response,
    });
  } catch (error) {
    logger.error("Chat endpoint error:", error);
    next(error);
  }
});

// @desc    Send a streaming chat message
// @route   POST /api/chat/stream
// @access  Private
router.post("/stream", protect, validateChatMessage, async (req, res, next) => {
  try {
    const { message, conversationId, agentConfigId } = req.body;
    const userId = req.user._id;

    let targetConversationId = conversationId;

    // If no conversation ID provided, create a new conversation
    if (!targetConversationId) {
      if (!agentConfigId) {
        // Use user's default agent config
        let defaultAgentConfig = await AgentConfig.findOne({
          userId,
          isDefault: true,
          status: "active",
        });

        if (!defaultAgentConfig) {
          return res.status(400).json({
            success: false,
            error: "No agent configuration specified and no default found",
          });
        }

        const newConversation = await conversationService.createConversation(
          userId,
          defaultAgentConfig._id,
          "New Chat"
        );
        targetConversationId = newConversation._id;
      } else {
        const newConversation = await conversationService.createConversation(
          userId,
          agentConfigId,
          "New Chat"
        );
        targetConversationId = newConversation._id;
      }
    }

    // Set up Server-Sent Events
    res.writeHead(200, {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Headers": "Cache-Control",
    });

    try {
      // Send streaming response
      for await (const chunk of conversationService.sendMessageStreaming(
        targetConversationId,
        userId,
        message
      )) {
        res.write(`data: ${JSON.stringify(chunk)}\n\n`);
      }

      res.write("data: [DONE]\n\n");
      res.end();
    } catch (streamError) {
      logger.error("Streaming error:", streamError);
      res.write(
        `data: ${JSON.stringify({
          error: "Streaming failed",
          message: streamError.message,
        })}\n\n`
      );
      res.end();
    }
  } catch (error) {
    logger.error("Chat streaming endpoint error:", error);
    if (!res.headersSent) {
      next(error);
    }
  }
});

module.exports = router;
