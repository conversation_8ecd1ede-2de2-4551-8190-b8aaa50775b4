const mongoose = require('mongoose');

const agentConfigSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Agent name is required'],
    trim: true,
    maxlength: [100, 'Agent name cannot exceed 100 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  isPublic: {
    type: Boolean,
    default: false
  },
  isDefault: {
    type: Boolean,
    default: false
  },
  personality: {
    systemPrompt: {
      type: String,
      required: [true, 'System prompt is required'],
      maxlength: [2000, 'System prompt cannot exceed 2000 characters']
    },
    traits: [{
      type: String,
      trim: true,
      maxlength: [50, 'Trait cannot exceed 50 characters']
    }],
    tone: {
      type: String,
      enum: ['professional', 'casual', 'friendly', 'formal', 'humorous', 'empathetic'],
      default: 'friendly'
    },
    expertise: [{
      type: String,
      trim: true,
      maxlength: [100, 'Expertise area cannot exceed 100 characters']
    }]
  },
  modelConfig: {
    provider: {
      type: String,
      enum: ['openai', 'anthropic', 'google', 'local'],
      required: true,
      default: 'openai'
    },
    model: {
      type: String,
      required: true,
      default: 'gpt-3.5-turbo'
    },
    temperature: {
      type: Number,
      min: [0, 'Temperature must be between 0 and 2'],
      max: [2, 'Temperature must be between 0 and 2'],
      default: 0.7
    },
    maxTokens: {
      type: Number,
      min: [1, 'Max tokens must be at least 1'],
      max: [8000, 'Max tokens cannot exceed 8000'],
      default: 2000
    },
    topP: {
      type: Number,
      min: [0, 'Top P must be between 0 and 1'],
      max: [1, 'Top P must be between 0 and 1'],
      default: 1
    },
    frequencyPenalty: {
      type: Number,
      min: [-2, 'Frequency penalty must be between -2 and 2'],
      max: [2, 'Frequency penalty must be between -2 and 2'],
      default: 0
    },
    presencePenalty: {
      type: Number,
      min: [-2, 'Presence penalty must be between -2 and 2'],
      max: [2, 'Presence penalty must be between -2 and 2'],
      default: 0
    }
  },
  capabilities: {
    webSearch: {
      type: Boolean,
      default: false
    },
    imageGeneration: {
      type: Boolean,
      default: false
    },
    codeExecution: {
      type: Boolean,
      default: false
    },
    fileUpload: {
      type: Boolean,
      default: false
    },
    memoryRetention: {
      type: Boolean,
      default: true
    }
  },
  constraints: {
    maxConversationLength: {
      type: Number,
      min: [1, 'Max conversation length must be at least 1'],
      max: [1000, 'Max conversation length cannot exceed 1000'],
      default: 50
    },
    responseTimeLimit: {
      type: Number,
      min: [1, 'Response time limit must be at least 1 second'],
      max: [300, 'Response time limit cannot exceed 300 seconds'],
      default: 30
    },
    dailyUsageLimit: {
      type: Number,
      min: [1, 'Daily usage limit must be at least 1'],
      default: 100
    },
    contentFilters: [{
      type: String,
      enum: ['profanity', 'violence', 'adult', 'political', 'religious'],
      default: []
    }]
  },
  customInstructions: [{
    trigger: {
      type: String,
      required: true,
      trim: true,
      maxlength: [100, 'Trigger cannot exceed 100 characters']
    },
    response: {
      type: String,
      required: true,
      trim: true,
      maxlength: [500, 'Response cannot exceed 500 characters']
    },
    isActive: {
      type: Boolean,
      default: true
    }
  }],
  analytics: {
    totalUsage: {
      type: Number,
      default: 0
    },
    averageRating: {
      type: Number,
      min: [1, 'Rating must be between 1 and 5'],
      max: [5, 'Rating must be between 1 and 5'],
      default: 0
    },
    totalRatings: {
      type: Number,
      default: 0
    },
    lastUsed: {
      type: Date
    }
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'deprecated'],
    default: 'active'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for usage statistics
agentConfigSchema.virtual('usageStats').get(function() {
  return {
    totalUsage: this.analytics.totalUsage,
    averageRating: this.analytics.averageRating,
    lastUsed: this.analytics.lastUsed
  };
});

// Indexes for better query performance
agentConfigSchema.index({ userId: 1, status: 1 });
agentConfigSchema.index({ isPublic: 1, status: 1 });
agentConfigSchema.index({ 'analytics.totalUsage': -1 });
agentConfigSchema.index({ 'analytics.averageRating': -1 });

// Pre-save middleware to ensure only one default per user
agentConfigSchema.pre('save', async function(next) {
  if (this.isDefault && this.isModified('isDefault')) {
    // Remove default flag from other configs for this user
    await this.constructor.updateMany(
      { userId: this.userId, _id: { $ne: this._id } },
      { isDefault: false }
    );
  }
  next();
});

// Instance method to increment usage
agentConfigSchema.methods.incrementUsage = function() {
  this.analytics.totalUsage += 1;
  this.analytics.lastUsed = new Date();
  return this.save();
};

// Instance method to add rating
agentConfigSchema.methods.addRating = function(rating) {
  const currentTotal = this.analytics.averageRating * this.analytics.totalRatings;
  this.analytics.totalRatings += 1;
  this.analytics.averageRating = (currentTotal + rating) / this.analytics.totalRatings;
  return this.save();
};

// Static method to find public agents
agentConfigSchema.statics.findPublic = function() {
  return this.find({ isPublic: true, status: 'active' }).sort({ 'analytics.averageRating': -1 });
};

// Static method to find user's agents
agentConfigSchema.statics.findByUser = function(userId) {
  return this.find({ userId, status: 'active' }).sort({ isDefault: -1, createdAt: -1 });
};

module.exports = mongoose.model('AgentConfig', agentConfigSchema);
