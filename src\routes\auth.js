const express = require("express");
const jwt = require("jsonwebtoken");
const router = express.Router();
const { protect } = require("../middleware/auth");
const {
  validateUserRegistration,
  validateUserLogin,
} = require("../middleware/validation");
const { User } = require("../models");
const logger = require("../utils/logger");

// Helper function to generate JWT token
const generateToken = (id) => {
  return jwt.sign({ id }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || "7d",
  });
};

// Helper function to send token response
const sendTokenResponse = (user, statusCode, res) => {
  const token = generateToken(user._id);

  const options = {
    expires: new Date(
      Date.now() +
        (parseInt(process.env.JWT_COOKIE_EXPIRE) || 7) * 24 * 60 * 60 * 1000
    ),
    httpOnly: true,
  };

  if (process.env.NODE_ENV === "production") {
    options.secure = true;
  }

  res
    .status(statusCode)
    .cookie("token", token, options)
    .json({
      success: true,
      token,
      data: {
        user: user.toJSON(),
      },
    });
};

// @desc    Register user
// @route   POST /api/auth/register
// @access  Public
router.post("/register", validateUserRegistration, async (req, res, next) => {
  try {
    const { username, email, password, firstName, lastName } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ email }, { username }],
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: "User with this email or username already exists",
      });
    }

    // Create user
    const user = await User.create({
      username,
      email,
      password,
      firstName,
      lastName,
    });

    logger.info("User registered", {
      userId: user._id,
      username: user.username,
      email: user.email,
    });

    sendTokenResponse(user, 201, res);
  } catch (error) {
    logger.error("Registration error:", error);
    next(error);
  }
});

// @desc    Login user
// @route   POST /api/auth/login
// @access  Public
router.post("/login", validateUserLogin, async (req, res, next) => {
  try {
    const { email, password } = req.body;

    // Find user by email and include password
    const user = await User.findOne({ email }).select("+password");

    if (!user) {
      return res.status(401).json({
        success: false,
        error: "Invalid credentials",
      });
    }

    // Check if user is active
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        error: "Account is deactivated",
      });
    }

    // Check password
    const isMatch = await user.comparePassword(password);

    if (!isMatch) {
      return res.status(401).json({
        success: false,
        error: "Invalid credentials",
      });
    }

    // Update last login
    await user.updateLastLogin();

    logger.info("User logged in", {
      userId: user._id,
      username: user.username,
      email: user.email,
    });

    sendTokenResponse(user, 200, res);
  } catch (error) {
    logger.error("Login error:", error);
    next(error);
  }
});

// @desc    Logout user / clear cookie
// @route   POST /api/auth/logout
// @access  Private
router.post("/logout", protect, (req, res) => {
  res.cookie("token", "none", {
    expires: new Date(Date.now() + 10 * 1000),
    httpOnly: true,
  });

  logger.info("User logged out", {
    userId: req.user._id,
    username: req.user.username,
  });

  res.status(200).json({
    success: true,
    message: "Logged out successfully",
  });
});

// @desc    Get current logged in user
// @route   GET /api/auth/me
// @access  Private
router.get("/me", protect, async (req, res, next) => {
  try {
    const user = await User.findById(req.user._id);

    res.status(200).json({
      success: true,
      data: {
        user,
      },
    });
  } catch (error) {
    logger.error("Get user profile error:", error);
    next(error);
  }
});

// @desc    Update user profile
// @route   PUT /api/auth/me
// @access  Private
router.put("/me", protect, async (req, res, next) => {
  try {
    const userId = req.user._id;
    const allowedUpdates = ["firstName", "lastName", "preferences"];
    const updates = {};

    // Filter allowed updates
    Object.keys(req.body).forEach((key) => {
      if (allowedUpdates.includes(key)) {
        updates[key] = req.body[key];
      }
    });

    const user = await User.findByIdAndUpdate(userId, updates, {
      new: true,
      runValidators: true,
    });

    logger.info("User profile updated", {
      userId,
      updates: Object.keys(updates),
    });

    res.status(200).json({
      success: true,
      data: {
        user,
      },
    });
  } catch (error) {
    logger.error("Update user profile error:", error);
    next(error);
  }
});

// @desc    Change password
// @route   PUT /api/auth/password
// @access  Private
router.put("/password", protect, async (req, res, next) => {
  try {
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        error: "Current password and new password are required",
      });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({
        success: false,
        error: "New password must be at least 6 characters long",
      });
    }

    // Get user with password
    const user = await User.findById(req.user._id).select("+password");

    // Check current password
    const isMatch = await user.comparePassword(currentPassword);

    if (!isMatch) {
      return res.status(400).json({
        success: false,
        error: "Current password is incorrect",
      });
    }

    // Update password
    user.password = newPassword;
    await user.save();

    logger.info("User password changed", {
      userId: user._id,
      username: user.username,
    });

    res.status(200).json({
      success: true,
      message: "Password updated successfully",
    });
  } catch (error) {
    logger.error("Change password error:", error);
    next(error);
  }
});

// @desc    Deactivate account
// @route   PUT /api/auth/deactivate
// @access  Private
router.put("/deactivate", protect, async (req, res, next) => {
  try {
    const userId = req.user._id;

    await User.findByIdAndUpdate(userId, { isActive: false });

    logger.info("User account deactivated", {
      userId,
      username: req.user.username,
    });

    res.status(200).json({
      success: true,
      message: "Account deactivated successfully",
    });
  } catch (error) {
    logger.error("Deactivate account error:", error);
    next(error);
  }
});

// @desc    Get user statistics
// @route   GET /api/auth/stats
// @access  Private
router.get("/stats", protect, async (req, res, next) => {
  try {
    const userId = req.user._id;
    const { Conversation, AgentConfig } = require("../models");

    const [conversationCount, agentConfigCount, totalMessages] =
      await Promise.all([
        Conversation.countDocuments({ userId, status: "active" }),
        AgentConfig.countDocuments({ userId, status: "active" }),
        Conversation.aggregate([
          { $match: { userId: userId, status: "active" } },
          { $group: { _id: null, total: { $sum: "$metadata.totalMessages" } } },
        ]),
      ]);

    const stats = {
      conversations: conversationCount,
      agentConfigs: agentConfigCount,
      totalMessages: totalMessages[0]?.total || 0,
      apiUsage: req.user.apiUsage,
    };

    res.status(200).json({
      success: true,
      data: stats,
    });
  } catch (error) {
    logger.error("Get user stats error:", error);
    next(error);
  }
});

module.exports = router;
