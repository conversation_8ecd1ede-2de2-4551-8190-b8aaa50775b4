const express = require("express");
const router = express.Router();
const { protect, optionalAuth } = require("../middleware/auth");
const {
  validateObjectId,
  validatePagination,
  validateAgentConfig,
} = require("../middleware/validation");
const { AgentConfig } = require("../models");
const aiService = require("../services/aiService");
const logger = require("../utils/logger");

// @desc    Get public agent configurations
// @route   GET /api/agent/configs/public
// @access  Public
router.get(
  "/configs/public",
  optionalAuth,
  validatePagination,
  async (req, res, next) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 20;
      const skip = (page - 1) * limit;

      const configs = await AgentConfig.find({
        isPublic: true,
        status: "active",
      })
        .populate("userId", "username")
        .sort({ "analytics.averageRating": -1, "analytics.totalUsage": -1 })
        .skip(skip)
        .limit(limit)
        .select("-__v");

      const total = await AgentConfig.countDocuments({
        isPublic: true,
        status: "active",
      });

      res.status(200).json({
        success: true,
        data: configs,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      });
    } catch (error) {
      logger.error("Get public agent configs error:", error);
      next(error);
    }
  }
);

// @desc    Get agent configurations
// @route   GET /api/agent/configs
// @access  Private
router.get("/configs", protect, validatePagination, async (req, res, next) => {
  try {
    const userId = req.user._id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const includePublic = req.query.includePublic === "true";

    const skip = (page - 1) * limit;

    let query = { userId, status: "active" };

    if (includePublic) {
      query = {
        $or: [
          { userId, status: "active" },
          { isPublic: true, status: "active" },
        ],
      };
    }

    const configs = await AgentConfig.find(query)
      .sort({ isDefault: -1, createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .select("-__v");

    const total = await AgentConfig.countDocuments(query);

    res.status(200).json({
      success: true,
      data: configs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    logger.error("Get agent configs error:", error);
    next(error);
  }
});

// @desc    Create agent configuration
// @route   POST /api/agent/configs
// @access  Private
router.post(
  "/configs",
  protect,
  validateAgentConfig,
  async (req, res, next) => {
    try {
      const userId = req.user._id;
      const configData = { ...req.body, userId };

      // Validate AI configuration
      if (!aiService.validateConfig(configData.modelConfig)) {
        return res.status(400).json({
          success: false,
          error: "Invalid AI model configuration",
        });
      }

      const agentConfig = new AgentConfig(configData);
      await agentConfig.save();

      logger.info("Agent config created", {
        configId: agentConfig._id,
        userId,
        name: agentConfig.name,
      });

      res.status(201).json({
        success: true,
        data: agentConfig,
      });
    } catch (error) {
      logger.error("Create agent config error:", error);
      next(error);
    }
  }
);

// @desc    Get specific agent configuration
// @route   GET /api/agent/configs/:id
// @access  Private
router.get(
  "/configs/:id",
  protect,
  validateObjectId,
  async (req, res, next) => {
    try {
      const configId = req.params.id;
      const userId = req.user._id;

      const agentConfig = await AgentConfig.findOne({
        _id: configId,
        $or: [{ userId }, { isPublic: true }],
        status: "active",
      });

      if (!agentConfig) {
        return res.status(404).json({
          success: false,
          error: "Agent configuration not found or access denied",
        });
      }

      res.status(200).json({
        success: true,
        data: agentConfig,
      });
    } catch (error) {
      logger.error("Get agent config error:", error);
      next(error);
    }
  }
);

// @desc    Update agent configuration
// @route   PUT /api/agent/configs/:id
// @access  Private
router.put(
  "/configs/:id",
  protect,
  validateObjectId,
  validateAgentConfig,
  async (req, res, next) => {
    try {
      const configId = req.params.id;
      const userId = req.user._id;
      const updates = req.body;

      // Validate AI configuration if modelConfig is being updated
      if (
        updates.modelConfig &&
        !aiService.validateConfig(updates.modelConfig)
      ) {
        return res.status(400).json({
          success: false,
          error: "Invalid AI model configuration",
        });
      }

      const agentConfig = await AgentConfig.findOneAndUpdate(
        {
          _id: configId,
          userId,
          status: "active",
        },
        updates,
        { new: true, runValidators: true }
      );

      if (!agentConfig) {
        return res.status(404).json({
          success: false,
          error: "Agent configuration not found or access denied",
        });
      }

      logger.info("Agent config updated", {
        configId,
        userId,
        updates: Object.keys(updates),
      });

      res.status(200).json({
        success: true,
        data: agentConfig,
      });
    } catch (error) {
      logger.error("Update agent config error:", error);
      next(error);
    }
  }
);

// @desc    Delete agent configuration
// @route   DELETE /api/agent/configs/:id
// @access  Private
router.delete(
  "/configs/:id",
  protect,
  validateObjectId,
  async (req, res, next) => {
    try {
      const configId = req.params.id;
      const userId = req.user._id;

      const agentConfig = await AgentConfig.findOneAndUpdate(
        {
          _id: configId,
          userId,
          status: "active",
        },
        { status: "deprecated" },
        { new: true }
      );

      if (!agentConfig) {
        return res.status(404).json({
          success: false,
          error: "Agent configuration not found or access denied",
        });
      }

      logger.info("Agent config deleted", {
        configId,
        userId,
      });

      res.status(200).json({
        success: true,
        message: "Agent configuration deleted successfully",
      });
    } catch (error) {
      logger.error("Delete agent config error:", error);
      next(error);
    }
  }
);

// @desc    Rate agent configuration
// @route   POST /api/agent/configs/:id/rate
// @access  Private
router.post(
  "/configs/:id/rate",
  protect,
  validateObjectId,
  async (req, res, next) => {
    try {
      const configId = req.params.id;
      const { rating } = req.body;

      if (!rating || rating < 1 || rating > 5) {
        return res.status(400).json({
          success: false,
          error: "Rating must be between 1 and 5",
        });
      }

      const agentConfig = await AgentConfig.findOne({
        _id: configId,
        status: "active",
      });

      if (!agentConfig) {
        return res.status(404).json({
          success: false,
          error: "Agent configuration not found",
        });
      }

      await agentConfig.addRating(rating);

      res.status(200).json({
        success: true,
        message: "Rating added successfully",
        data: {
          averageRating: agentConfig.analytics.averageRating,
          totalRatings: agentConfig.analytics.totalRatings,
        },
      });
    } catch (error) {
      logger.error("Rate agent config error:", error);
      next(error);
    }
  }
);

// @desc    Get available AI models
// @route   GET /api/agent/models
// @access  Private
router.get("/models", protect, async (req, res, next) => {
  try {
    const { provider } = req.query;

    if (provider) {
      const models = await aiService.getAvailableModels(provider);
      res.status(200).json({
        success: true,
        data: models,
      });
    } else {
      // Get models from all providers
      const providers = ["openai", "anthropic"];
      const allModels = [];

      for (const prov of providers) {
        try {
          const models = await aiService.getAvailableModels(prov);
          allModels.push(...models);
        } catch (error) {
          logger.warn(
            `Failed to get models for provider ${prov}:`,
            error.message
          );
        }
      }

      res.status(200).json({
        success: true,
        data: allModels,
      });
    }
  } catch (error) {
    logger.error("Get models error:", error);
    next(error);
  }
});

module.exports = router;
